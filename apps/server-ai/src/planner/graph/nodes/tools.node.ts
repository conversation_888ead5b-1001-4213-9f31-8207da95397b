import { Command } from '@langchain/langgraph';
import { AIMessage } from '@langchain/core/messages';
import { NodeNames, ToolNames } from '../constants';
import { StateAnnotation } from '../types/state.types';
import { fetchDocumentWithImages } from '../openapi';

/**
 * 工具节点 - 处理工具执行
 * 负责执行各种工具调用，如文档获取、任务转交等
 */
export async function toolsNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('Tools节点开始执行工具...', state.messages);

  // 获取最后一条消息中的工具调用
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;

  if (!lastMessage?.tool_calls || lastMessage.tool_calls.length === 0) {
    console.log('没有找到工具调用，返回coordinator');
    return new Command({
      goto: NodeNames.COORDINATOR,
    });
  }

  const toolCall = lastMessage.tool_calls[0];
  console.log('执行工具:', toolCall.name, toolCall.args);

  try {
    // 根据工具名称执行相应的工具
    if (toolCall.name === ToolNames.FETCH_DOCUMENT) {
      const { url } = toolCall.args as { url: string };

      console.log('开始获取文档:', url);

      // 使用增强版文档获取功能，支持图片处理
      const { content, images } = await fetchDocumentWithImages(url);

      console.log(
        `文档获取成功，内容长度: ${content.length}，图片数量: ${images.length}`,
      );

      // 返回到coordinator进行PRD判断
      return new Command({
        goto: NodeNames.COORDINATOR,
        update: {
          documentContent: content,
          images: images,
          processingStatus: `文档获取成功，内容长度: ${content.length}字符，图片: ${images.length}张`,
        },
      });
    } else if (toolCall.name === ToolNames.HANDOFF_TO_PLANNER) {
      const { reason } = toolCall.args as { reason: string };

      console.log(
        'handoff_to_planner工具调用，路由到PRD_PARSER节点，原因:',
        reason,
      );

      return new Command({
        goto: NodeNames.PRD_PARSER,
        update: {
          isPRDRequest: true,
          processingStatus: `已识别为PRD请求，原因: ${reason}`,
        },
      });
    }

    // 未知工具，返回coordinator
    console.log('未知工具:', toolCall.name);
    return new Command({
      goto: NodeNames.COORDINATOR,
      update: {
        processingStatus: `未知工具调用: ${toolCall.name}`,
      },
    });
  } catch (error) {
    console.error('工具执行失败:', error);

    // 根据工具类型处理错误
    if (toolCall.name === ToolNames.FETCH_DOCUMENT) {
      return new Command({
        goto: NodeNames.COORDINATOR,
        update: {
          documentContent: `获取文档失败: ${error.message}`,
          error: `文档获取错误: ${error.message}`,
          processingStatus: '文档获取失败',
        },
      });
    }

    // 其他错误，直接返回coordinator
    return new Command({
      goto: NodeNames.COORDINATOR,
      update: {
        error: `工具执行失败: ${error.message}`,
        processingStatus: '工具执行遇到错误',
      },
    });
  }
}
