import { Command } from '@langchain/langgraph';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { createLLM } from '../utils/llm.utils';
import { createTools } from '../tools';
import { NodeNames, ToolNames } from '../constants';
import { StateAnnotation } from '../types/state.types';
import { createMessageWithImages } from '../utils/message.utils';

/**
 * Coordinator节点 - 判断是否为PRD需求
 * 负责初步判断用户输入类型并路由到相应的处理节点
 */
export async function coordinatorNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('Coordinator节点开始判断用户输入...', state.originalPRD);

  const llm = createLLM();
  const tools = createTools();
  const coordinatorLLM = llm.bindTools(tools);

  // 检查最新消息是否包含图片
  const lastMessage = state.messages[state.messages.length - 1];
  const hasImage =
    lastMessage &&
    Array.isArray(lastMessage.content) &&
    lastMessage.content.some((item) => item.type === 'image_url');

  // 检查是否有处理过的图片数据
  const hasProcessedImages = state.images && state.images.length > 0;

  // 如果已经有文档内容，说明是从工具节点返回的，直接进行PRD判断
  if (state.documentContent) {
    console.log('检测到已获取的文档内容，进行PRD判断');
    const combinedInput = `${state.originalPRD}\n\n获取的文档内容：\n${state.documentContent}`;

    const prdJudgmentPromptText = `
你是一个智能助手协调员，专门负责判断用户输入是否为产品需求文档(PRD)相关的请求。

## 用户输入和文档内容
${combinedInput}

## 判断标准
请根据以下标准判断是否为PRD相关请求：
1. 是否包含产品功能描述
2. 是否包含需求说明
3. 是否要求进行产品规划或任务分解
4. 是否涉及产品开发相关内容

## 处理指令
- 如果是PRD相关请求：调用 handoff_to_planner 工具并提供判断理由
- 如果不是PRD请求：直接回复礼貌的说明信息

## 礼貌回复模板
很抱歉，我是专门处理产品需求文档(PRD)分析和任务分解的助手。您的问题似乎不在我的专业范围内。

我的主要功能包括：
- 分析和结构化产品需求文档
- 将PRD转换为具体的开发任务
- 提供技术实现建议和项目规划
- 处理包含图片的产品需求文档

如果您有产品需求相关的问题，我很乐意为您提供帮助！
`;

    // 如果有图片信息，使用包含图片的消息格式
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(
        prdJudgmentPromptText,
        state.images,
      );
      response = await coordinatorLLM.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await coordinatorLLM.invoke(prdJudgmentPromptText);
    }

    if (response.tool_calls && response.tool_calls.length > 0) {
      const toolCall = response.tool_calls[0];
      if (toolCall.name === ToolNames.HANDOFF_TO_PLANNER) {
        console.log('识别为PRD请求，路由到PRD_PARSER节点');
        return new Command({
          goto: NodeNames.PRD_PARSER,
          update: {
            isPRDRequest: true,
            originalPRD: combinedInput,
          },
        });
      }
    }

    // 不是PRD请求
    const userMessage =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    return new Command({
      goto: 'END',
      update: {
        isPRDRequest: false,
        messages: [
          new AIMessage({
            content: userMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  }

  // 初次判断：检查是否包含文档链接、图片链接或是否为PRD请求
  const promptText = `
你是一个智能助手协调员，专门负责判断用户输入类型并进行相应的路由处理。

## 用户输入
${state.originalPRD}

${hasImage ? '注意：用户消息中包含图片内容，请考虑图片在PRD分析中的作用。' : ''}
${hasProcessedImages ? `已处理的图片信息：${state.images.map((img) => `${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已转换为base64)'}`).join(', ')}` : ''}

## 判断标准和处理逻辑
请按照以下优先级进行判断和处理：

### 1. 文档链接检测
如果用户提供了文档链接（http/https URL），请调用 fetch_document 工具获取文档内容

### 2. PRD需求判断
判断是否为产品需求文档相关请求：
- 是否包含产品功能描述
- 是否包含需求说明
- 是否要求进行产品规划或任务分解
- 是否涉及产品开发相关内容

### 3. 处理指令
- 包含文档链接：先调用 fetch_document 工具获取内容
- 是PRD相关请求：调用 handoff_to_planner 工具并提供判断理由
- 不是PRD请求：直接回复礼貌的说明信息

## 礼貌回复模板
很抱歉，我是专门处理产品需求文档(PRD)分析和任务分解的助手。您的问题似乎不在我的专业范围内。

我的主要功能包括：
- 分析和结构化产品需求文档
- 将PRD转换为具体的开发任务
- 提供技术实现建议和项目规划
- 处理包含图片的产品需求文档

如果您有产品需求相关的问题，我很乐意为您提供帮助！
`;

  try {
    // 如果有图片信息，使用包含图片的消息格式
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await coordinatorLLM.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await coordinatorLLM.invoke(promptText);
    }
    console.log('Coordinator节点完成判断', response);

    // 检查是否有工具调用
    if (response.tool_calls && response.tool_calls.length > 0) {
      console.log('检测到工具调用，路由到工具节点');
      return new Command({
        goto: NodeNames.TOOLS,
        update: {
          messages: [response],
        },
      });
    }

    // 如果没有工具调用，说明不是PRD请求
    console.log('非PRD请求，返回礼貌响应并结束');
    const userMessage =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    return new Command({
      goto: 'END',
      update: {
        isPRDRequest: false,
        messages: [
          new AIMessage({
            content: userMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  } catch (error) {
    console.error('Coordinator节点处理失败:', error);

    // 处理不同类型的错误
    let errorMessage = '系统暂时不可用，请稍后重试。';

    if (
      error.message.includes('AbortError') ||
      error.message.includes('Request was aborted')
    ) {
      errorMessage = '请求超时，请检查网络连接后重试。';
    } else if (error.message.includes('ENOTFOUND')) {
      errorMessage = '网络连接失败，请检查网络设置。';
    } else if (error.message.includes('401') || error.message.includes('403')) {
      errorMessage = 'API认证失败，请检查配置。';
    } else if (error.message.includes('429')) {
      errorMessage = '请求过于频繁，请稍后重试。';
    }

    // 返回错误响应而不是抛出异常
    return new Command({
      goto: 'END',
      update: {
        isPRDRequest: false,
        messages: [
          new AIMessage({
            content: errorMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  }
}
