import { Command } from '@langchain/langgraph';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { createStructuredLLM } from '../utils/llm.utils';
import { NodeNames, TaskType, TaskPriority, TaskStatus } from '../constants';
import { StateAnnotation, Task } from '../types/state.types';
import {
  createMessageWithImages,
  createErrorMessage,
} from '../utils/message.utils';

/**
 * 后端规划器节点 - 生成后端开发任务
 * 专门负责分析PRD中的后端需求并生成相应的开发任务
 */
export async function backendPlannerNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('BACKEND_PLANNER节点开始生成后端任务...');

  const llm = createStructuredLLM();

  // 解析前端任务以了解API需求
  let frontendTasks: Task[] = [];
  try {
    if (state.frontendTaskJSON) {
      frontendTasks = JSON.parse(state.frontendTaskJSON);
    }
  } catch (error) {
    console.warn('解析前端任务失败:', error);
  }

  // 构建后端任务规划提示
  const promptText = `
你是一个专业的后端架构师和开发专家，精通现代后端技术栈和微服务架构。你的任务是基于结构化的PRD文档和前端任务需求，分析后端需求并生成详细的后端开发任务列表。

## 结构化PRD文档
${state.structuredPRD}

${
  state.planFeedback
    ? `
## 用户反馈信息
用户对任务规划提供了以下反馈，请在重新规划时重点考虑：
${state.planFeedback}

请基于这些反馈信息调整后端任务规划，确保满足用户的具体要求。
`
    : ''
}

## 前端任务需求分析
${
  frontendTasks.length > 0
    ? `
前端已规划的任务包括：
${frontendTasks.map((task) => `- ${task.title}: ${task.description}`).join('\n')}

请分析这些前端任务对后端API和服务的需求。
`
    : '前端任务信息暂未提供，请基于PRD独立分析后端需求。'
}

${
  state.images && state.images.length > 0
    ? `
## 相关图片信息
PRD中包含以下图片，请在任务规划时考虑：
${state.images.map((img, index) => `图片${index + 1}: ${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已处理)'}`).join('\n')}

如果图片包含数据流程图、架构图或业务逻辑说明，请在任务的details字段中详细描述相关的实现要求。
`
    : ''
}

## 技术栈说明
- **开发语言**: Groovy + Spring Boot
- **数据库**: MySQL/PostgreSQL + Redis缓存
- **API框架**: Spring Web + RESTful API
- **安全认证**: Spring Security + JWT
- **数据访问**: MyBatis/JPA + 连接池
- **消息队列**: RabbitMQ/Kafka (如需要)
- **监控日志**: Logback + Micrometer
- **部署方式**: Docker + K8s

## 任务规划原则
1. **分层架构**: Controller -> Service -> Repository 清晰分层
2. **领域驱动**: 按业务领域划分服务和模块
3. **API优先**: 设计清晰的RESTful API接口
4. **数据一致性**: 考虑事务管理和数据完整性
5. **性能优化**: 缓存策略、查询优化、异步处理
6. **安全可靠**: 权限控制、数据验证、异常处理

## 任务分类指导
### 基础设施任务 (优先级: high)
- 项目初始化和配置
- 数据库设计和初始化
- 基础框架搭建
- 安全认证体系

### 核心业务任务 (优先级: high/medium)
- 业务实体和数据模型
- 核心业务逻辑实现
- API接口开发
- 数据访问层实现

### 增强功能任务 (优先级: medium/low)
- 缓存策略实现
- 性能优化
- 监控和日志
- 部署和运维配置

## 响应格式要求
请严格按照以下JSON格式返回后端任务列表：

\`\`\`json
{
  "tasks": [
    {
      "id": 100,
      "title": "任务标题",
      "description": "任务描述",
      "status": "pending",
      "prompt": "给开发者的详细指令",
      "dependencies": [依赖的任务ID数组],
      "priority": "high|medium|low",
      "details": "详细的实现指导，包括技术细节、API设计等",
      "testStrategy": "测试和验证方法",
      "type": "backend",
      "estimatedHours": 预估工时,
      "assignee": "后端开发工程师"
    }
  ],
  "summary": {
    "totalTasks": 任务总数,
    "estimatedDuration": "预估总工期",
    "techStack": ["Groovy", "Spring Boot", "MySQL"],
    "apiEndpoints": ["主要API端点列表"],
    "databaseTables": ["主要数据表列表"]
  }
}
\`\`\`

## 任务ID编号规则
- 后端任务ID从100开始编号 (100, 101, 102...)
- 前端任务ID范围是1-99

## 任务示例参考
\`\`\`json
{
  "id": 100,
  "title": "初始化Spring Boot项目",
  "description": "创建基于Groovy和Spring Boot的后端项目基础架构",
  "status": "pending",
  "prompt": "请使用Spring Boot和Groovy创建后端项目，包括基础配置、数据库连接和安全配置",
  "dependencies": [],
  "priority": "high",
  "details": "1. 创建Spring Boot项目结构\n2. 配置Groovy编译环境\n3. 集成MySQL数据库连接\n4. 配置Redis缓存\n5. 设置Spring Security基础配置\n6. 创建基础的Controller、Service、Repository层",
  "testStrategy": "验证项目能够正常启动，数据库连接正常，基础API可访问",
  "type": "backend",
  "estimatedHours": 12,
  "assignee": "后端开发工程师"
}
\`\`\`

## API设计指导
请在任务中包含以下API设计考虑：
1. **RESTful规范**: 使用标准的HTTP方法和状态码
2. **统一响应格式**: 定义标准的API响应结构
3. **参数验证**: 输入参数的验证和错误处理
4. **分页查询**: 列表查询的分页和排序
5. **权限控制**: API访问权限和用户认证
6. **文档生成**: Swagger/OpenAPI文档

## 数据库设计指导
请在任务中考虑：
1. **表结构设计**: 主键、外键、索引设计
2. **数据类型**: 合适的字段类型和长度
3. **约束条件**: 唯一约束、非空约束等
4. **性能优化**: 索引策略和查询优化
5. **数据迁移**: 版本升级和数据迁移脚本

## 重要提示
1. 后端任务ID从100开始，避免与前端任务冲突
2. 考虑前端任务的API需求，确保接口匹配
3. 每个任务的details字段要包含具体的技术实现指导
4. 优先考虑数据安全和业务逻辑的正确性
5. 估算工时要包含开发、测试和文档编写时间

现在请基于以上PRD内容和前端需求，生成详细的后端开发任务列表：
`;

  try {
    // 使用LLM生成后端任务
    let response: AIMessage;

    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await llm.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await llm.invoke(promptText);
    }

    console.log('BACKEND_PLANNER节点完成后端任务生成', response);

    // 解析LLM响应
    const responseContent =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    // 提取JSON内容
    const jsonMatch = responseContent.match(/```json\s*([\s\S]*?)\s*```/);
    let parsedResponse: { tasks: Task[]; summary: any };

    if (jsonMatch) {
      try {
        parsedResponse = JSON.parse(jsonMatch[1]);
      } catch (parseError) {
        console.error('后端任务JSON解析失败:', parseError);
        throw new Error('后端任务生成失败：JSON格式无效');
      }
    } else {
      // 尝试直接解析整个响应
      try {
        parsedResponse = JSON.parse(responseContent);
      } catch (parseError) {
        console.error('无法解析后端任务响应为JSON:', responseContent);
        throw new Error('后端任务响应格式不正确');
      }
    }

    // 验证和标准化任务数据
    if (!parsedResponse.tasks || !Array.isArray(parsedResponse.tasks)) {
      throw new Error('后端任务列表格式无效');
    }

    // 标准化任务数据，确保ID从100开始
    const standardizedTasks = parsedResponse.tasks.map((task, index) => ({
      ...task,
      id: task.id || 100 + index,
      type: TaskType.BACKEND,
      status: TaskStatus.PENDING,
      priority: task.priority || TaskPriority.MEDIUM,
      estimatedHours: task.estimatedHours || 12,
      assignee: task.assignee || '后端开发工程师',
    }));

    console.log(`后端任务生成完成，共${standardizedTasks.length}个任务`);

    // 转换为JSON字符串存储
    const backendTaskJSON = JSON.stringify(standardizedTasks, null, 2);

    // 合并前后端任务生成完整的任务列表
    const allTasks = [...frontendTasks, ...standardizedTasks];
    const taskJSON = JSON.stringify(allTasks, null, 2);

    return new Command({
      goto: NodeNames.PLANNER_FEEDBACK,
      update: {
        backendTaskJSON: backendTaskJSON,
        taskJSON: taskJSON,
        processingStatus: `任务规划完成，共生成${allTasks.length}个任务（前端${frontendTasks.length}个，后端${standardizedTasks.length}个）`,
      },
    });
  } catch (error) {
    console.error('BACKEND_PLANNER节点处理失败:', error);

    // 创建默认的后端任务作为fallback
    const fallbackTasks: Task[] = [
      {
        id: 100,
        title: '后端项目初始化',
        description: '创建基于Groovy和Spring Boot的后端项目基础结构',
        status: TaskStatus.PENDING,
        prompt: '请初始化Spring Boot后端项目，包括基础配置和数据库连接',
        dependencies: [],
        priority: TaskPriority.HIGH,
        details:
          '由于自动任务生成失败，请手动创建后端项目基础结构，包括数据库设计和API接口',
        testStrategy: '验证项目能够正常启动，数据库连接正常，基础API可访问',
        type: TaskType.BACKEND,
        estimatedHours: 20,
        assignee: '后端开发工程师',
      },
    ];

    const fallbackJSON = JSON.stringify(fallbackTasks, null, 2);

    // 合并前后端任务生成完整的任务列表（fallback情况）
    const allTasks = [...frontendTasks, ...fallbackTasks];
    const taskJSON = JSON.stringify(allTasks, null, 2);

    return new Command({
      goto: NodeNames.PLANNER_FEEDBACK,
      update: {
        backendTaskJSON: fallbackJSON,
        taskJSON: taskJSON,
        error: createErrorMessage(error, '后端任务规划'),
        processingStatus: '后端任务规划遇到问题，已生成基础任务',
      },
    });
  }
}
