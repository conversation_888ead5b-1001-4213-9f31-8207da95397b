import { Command } from '@langchain/langgraph';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { createStructuredLLM } from '../utils/llm.utils';
import { NodeNames, PRDCompleteness } from '../constants';
import { StateAnnotation, PRDParserResponse } from '../types/state.types';
import {
  createMessageWithImages,
  createErrorMessage,
} from '../utils/message.utils';

/**
 * 可扩展的PRD要求配置
 */
interface PRDRequirementConfig {
  frontend: {
    [key: string]: {
      label: string;
      description: string;
      required: boolean;
      controlType?: string; // 控件类型：input-text, select, textarea, radio, checkbox等
      options?: Array<{ value: string; label: string }>; // 用于select、radio、checkbox等的选项
      source?: string | { url: string; method?: string; data?: any }; // 数据源URL或配置对象
    };
  };
  backend: {
    [key: string]: {
      label: string;
      description: string;
      required: boolean;
      controlType?: string;
      options?: Array<{ value: string; label: string }>;
      source?: string | { url: string; method?: string; data?: any };
    };
  };
  common: {
    [key: string]: {
      label: string;
      description: string;
      required: boolean;
      controlType?: string;
      options?: Array<{ value: string; label: string }>;
      source?: string | { url: string; method?: string; data?: any };
    };
  };
}

/**
 * 默认的PRD要求配置 - 可以根据需要扩展
 */
const DEFAULT_PRD_REQUIREMENTS: PRDRequirementConfig = {
  frontend: {
    ui_design: {
      label: '页面布局',
      description: '能表述页面布局的页面原型图或详细的界面描述',
      required: true,
      controlType: 'textarea',
    },
    user_interaction: {
      label: '用户交互、组件交互',
      description: '用户交互流程 或者 组件联动流程的说明和操作逻辑',
      required: true,
      controlType: 'textarea',
    },
  },
  backend: {
    datasource: {
      label: '数据表依赖',
      description: '依赖的数据表、数据信息',
      required: true,
      controlType: 'textarea',
    },
    api_dependencies: {
      label: '组件对接口的依赖',
      description: 'API接口定义和规范',
      required: true,
      controlType: 'textarea',
    },
  },
  common: {},
};

/**
 * 生成可扩展的PRD要求描述
 */
function generateRequirementsDescription(config: PRDRequirementConfig): string {
  const sections = [
    {
      title: '前端要求',
      key: 'frontend' as keyof PRDRequirementConfig,
    },
    {
      title: '后端要求',
      key: 'backend' as keyof PRDRequirementConfig,
    },
    {
      title: '通用要求',
      key: 'common' as keyof PRDRequirementConfig,
    },
  ];

  return sections
    .map((section) => {
      const requirements = config[section.key];
      if (!requirements || Object.keys(requirements).length === 0) {
        return `**${section.title}：** 无要求`;
      }
      const requiredItems = Object.entries(requirements)
        .filter(([, req]) => req.required)
        .map(([, req]) => `- ${req.description}`)
        .join('\n');

      const optionalItems = Object.entries(requirements)
        .filter(([, req]) => !req.required)
        .map(([, req]) => `- ${req.description} (可选)`)
        .join('\n');

      let sectionContent = `**${section.title}：**\n${requiredItems}`;
      if (optionalItems) {
        sectionContent += `\n${optionalItems}`;
      }

      return sectionContent;
    })
    .join('\n\n');
}

/**
 * 生成可扩展的XML模板
 */
function generateXMLTemplate(): string {
  return `
<page class="data-management-system">
    <alert></alert>
    <!-- 筛选区域 -->
    <filterSection class="filter-container">
        <!-- 顶部搜索区 -->
        <searchArea class="search-container">
            <inputGroup>
                <select placeholder="选择搜索类型"/>
                <input type="text" placeholder="输入关键词搜索"/>
                <otherControls></otherControls>
            </inputGroup>
        </searchArea>

        <!-- 筛选器区 -->
        <filterArea class="filter-rows">
            <!-- 作者管理类目筛选行 -->
            <filterRow class="filter-row">
                <label>作者管理类目:</label>
                <horizontalSelect mode="multiple">
                    <option value="all">全部</option>
                    <option value="news">时政资讯</option>
                    <!-- 其他选项 -->
                </horizontalSelect>
            </filterRow>

            <!-- 视频词图类目筛选行 -->
            <filterRow class="filter-row">
                <label>视频词图类目:</label>
                <cascadeSelect>
                    <option value="all">全部</option>
                    <!-- 级联选项 -->
                </cascadeSelect>
            </filterRow>

            <!-- 高级筛选行 -->
            <otherFilterRow class="filter-row">
                <label>xxx:</label>
                <radioGroup>
                    <radio value="all">全部</radio>
                    <radio value="social">xx</radio>
                    <radio value="weather">xx</radio>
                </radioGroup>
            </otherFilterRow>

        </filterArea>

        <!-- 已选区域 -->
        <selectedFilters class="selected-tags">
            <tag closable>时政资讯</tag>
            <tag closable>社会事件</tag>
            <!-- 其他已选标签 -->
        </selectedFilters>
    </filterSection>

    <!-- 表格区域 -->
    <tableSection class="table-container">
        <!-- 表格头部 -->
        <tableHeader class="table-header">
            <div class="left">共 2 个视频</div>
            <div class="right">
                <button>下载明细</button>
            </div>
        </tableHeader>

        <!-- 表格主体 -->
        <table class="data-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>视频</th>
                    <th>有效播放次数</th>
                    <th>点赞次数</th>
                    <th>评论次数</th>
                    <th>分享次数</th>
                    <th>收藏次数</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 数据行 -->
                <tr>
                    <td>1</td>
                    <td>
                        <videoCard>
                            <thumbnail/>
                            <videoInfo/>
                        </videoCard>
                    </td>
                    <!-- 其他数据列 -->
                    <td>
                        <actionButtons>
                            <button>视频详情</button>
                            <button>提报热点</button>
                            <!-- 其他操作按钮 -->
                        </actionButtons>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 分页器 -->
        <pagination class="table-pagination">
            <pageSize/>
            <pageNumber/>
            <totalCount/>
        </pagination>
    </tableSection>
</page>`;
}

/**
 * 生成PRD解析提示
 */
function generatePrompt(
  state: typeof StateAnnotation.State,
  requirementsDescription: string,
  xmlTemplate: string,
  requirementsConfig: PRDRequirementConfig,
): string {
  return `
你是一个专业的全栈产品经理和技术架构师，具备丰富的前后端开发经验。你的任务是分析用户输入的产品需求描述，评估其完整性，并提供相应的处理建议。

## 用户输入的PRD内容
${state.originalPRD}

${
  state.images && state.images.length > 0
    ? `
## 附加的图片信息
用户提供了以下图片作为PRD的补充材料：
${state.images.map((img, index) => `图片${index + 1}: ${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已处理)'}`).join('\n')}
请结合图片内容进行PRD分析。如果图片包含原型图、界面设计、流程图或功能说明，请在分析中重点考虑这些视觉信息。
`
    : ''
}

## 分析任务
请按照以下标准评估PRD的完整性，并提供相应的响应：

### 完整性评估标准
一个完整的PRD应该包含：

${requirementsDescription}

## 响应格式要求
请严格按照以下JSON格式返回你的分析结果：

\`\`\`json
{
  "type": "complete" | "incomplete",
  "content": "具体内容"
}
\`\`\`
### 如果PRD完整 (type: "complete")
- content字段包含结构化的XML格式PRD文档
- 使用以下XML结构模板：

\`\`\`xml
${xmlTemplate}
\`\`\`

### 如果PRD不完整 (type: "incomplete")
- content字段包含AMIS表单的JSON schema，用于收集缺失信息

生成AMIS表单时，请根据缺失的信息类别动态创建表单字段。参考以下字段映射：

**前端相关字段：**
${Object.entries(requirementsConfig.frontend)
  .map(([key, req]) => {
    let result = `- ${key}: ${req.label} - ${req.description}`;
    if (req.controlType) result += ` (控件类型: ${req.controlType})`;
    if (req.options) result += ` (预设选项可用)`;
    if (req.source) result += ` (数据源可用)`;
    return result;
  })
  .join('\n')}

**后端相关字段：**
${Object.entries(requirementsConfig.backend)
  .map(([key, req]) => {
    let result = `- ${key}: ${req.label} - ${req.description}`;
    if (req.controlType) result += ` (控件类型: ${req.controlType})`;
    if (req.options) result += ` (预设选项可用)`;
    if (req.source) result += ` (数据源可用)`;
    return result;
  })
  .join('\n')}

**通用字段：**
${Object.entries(requirementsConfig.common)
  .map(([key, req]) => {
    let result = `- ${key}: ${req.label} - ${req.description}`;
    if (req.controlType) result += ` (控件类型: ${req.controlType})`;
    if (req.options) result += ` (预设选项可用)`;
    if (req.source) result += ` (数据源可用)`;
    return result;
  })
  .join('\n')}

AMIS表单示例：
\`\`\`json
{
  "type": "form",
  "title": "完善产品需求文档",
  "body": [
    {
      "type": "fieldSet",
      "title": "基本信息",
      "body": [
        {
          "name": "product_title",
          "type": "input-text",
          "label": "产品标题",
          "required": true,
          "placeholder": "请输入产品名称"
        }
      ]
    }
  ]
}
\`\`\`

## 重要提示
1. 仅返回JSON格式的响应，不要包含其他解释文字
2. 确保JSON格式正确，可以被解析
3. 如果有图片信息，请在分析中充分考虑视觉内容
4. 优先考虑技术实现的可行性和完整性
5. 对于模糊或不明确的需求，归类为不完整
6. 根据配置的要求标准进行评估，必需项缺失时标记为不完整

现在请开始分析并返回结果：
`;
}

/**
 * PRD解析器节点 - 处理和验证PRD内容
 * 负责将原始PRD转换为结构化格式并评估完整性
 */
export async function prdParserNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('PRD_PARSER节点开始处理原始PRD...');
  const llm = createStructuredLLM();

  // 使用默认配置
  const requirementsConfig = DEFAULT_PRD_REQUIREMENTS;
  const requirementsDescription =
    generateRequirementsDescription(requirementsConfig);
  const xmlTemplate = generateXMLTemplate();

  // 构建优化的PRD解析提示
  const promptText = generatePrompt(
    state,
    requirementsDescription,
    xmlTemplate,
    requirementsConfig,
  );

  try {
    // 使用LLM进行PRD分析
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await llm.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await llm.invoke(promptText);
    }

    console.log('PRD-Parser节点完成PRD结构化', response);

    // 解析LLM响应
    const responseContent =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    // 提取JSON内容
    const jsonMatch = responseContent.match(/```json\s*([\s\S]*?)\s*```/);
    let parsedResponse: PRDParserResponse;

    if (jsonMatch) {
      try {
        parsedResponse = JSON.parse(jsonMatch[1]);
      } catch (parseError) {
        console.error('JSON解析失败:', parseError);
        throw new Error('LLM返回的JSON格式无效');
      }
    } else {
      // 尝试直接解析整个响应
      try {
        parsedResponse = JSON.parse(responseContent);
      } catch (parseError) {
        console.error('无法解析LLM响应为JSON:', responseContent);
        throw new Error('LLM响应格式不正确，未找到有效的JSON');
      }
    }

    // 验证响应格式
    if (!parsedResponse.type || !parsedResponse.content) {
      throw new Error('LLM响应缺少必要字段');
    }

    // 根据分析结果决定下一步
    if (parsedResponse.type === PRDCompleteness.COMPLETE) {
      console.log('PRD信息完整，返回完整PRD并进入前端规划节点');
      return new Command({
        goto: NodeNames.FRONTEND_PLANNER,
        update: {
          structuredPRD: parsedResponse.content,
          prdAnalysis: JSON.stringify(parsedResponse),
        },
      });
    } else {
      console.log('PRD信息不完整，返回补充信息请求');
      return new Command({
        goto: NodeNames.PRD_FEEDBACK,
        update: {
          prdAnalysis: JSON.stringify(parsedResponse),
        },
      });
    }
  } catch (error) {
    console.error('PRD-Parser节点处理失败:', error);

    // 创建错误响应
    const errorResponse: PRDParserResponse = {
      type: 'incomplete',
      content: JSON.stringify({
        type: 'form',
        title: '系统错误 - 请重新提交',
        body: [
          {
            type: 'alert',
            level: 'danger',
            body: `处理PRD时发生错误：${createErrorMessage(error)}`,
          },
          {
            name: 'retry_prd',
            type: 'textarea',
            label: '请重新描述您的产品需求',
            required: true,
            placeholder: '请提供更详细的产品需求描述...',
          },
        ],
      }),
    };

    return new Command({
      goto: NodeNames.PRD_FEEDBACK,
      update: {
        prdAnalysis: JSON.stringify(errorResponse),
        error: createErrorMessage(error, 'PRD解析'),
      },
    });
  }
}

/**
 * 创建自定义PRD要求配置的辅助函数
 */
export function createCustomPRDRequirements(
  customConfig: Partial<PRDRequirementConfig>,
): PRDRequirementConfig {
  return {
    frontend: {
      ...DEFAULT_PRD_REQUIREMENTS.frontend,
      ...customConfig.frontend,
    },
    backend: { ...DEFAULT_PRD_REQUIREMENTS.backend, ...customConfig.backend },
    common: { ...DEFAULT_PRD_REQUIREMENTS.common, ...customConfig.common },
  };
}

/**
 * 获取默认PRD要求配置
 */
export function getDefaultPRDRequirements(): PRDRequirementConfig {
  return DEFAULT_PRD_REQUIREMENTS;
}

/**
 * 验证PRD要求配置的有效性
 */
export function validatePRDRequirements(config: PRDRequirementConfig): boolean {
  const sections = ['frontend', 'backend', 'common'] as const;

  for (const section of sections) {
    const requirements = config[section];
    if (!requirements || typeof requirements !== 'object') {
      return false;
    }
    for (const [key, req] of Object.entries(requirements)) {
      if (!req.label || !req.description || typeof req.required !== 'boolean') {
        console.warn(`Invalid requirement configuration for ${section}.${key}`);
        return false;
      }
    }
  }

  return true;
}

/**
 * 导出类型定义供外部使用
 */
export type { PRDRequirementConfig };
