# 优化后的Planner系统

## 概述

这是一个重新设计和优化的产品需求文档(PRD)分析和任务规划系统。系统采用模块化设计，将原来的单一graph.ts文件拆分成多个专门的节点和工具模块，提供更好的可维护性和扩展性。

## 主要改进

### 1. 前后端任务分离
- **问题**: 原系统生成的任务混合了前后端技术栈，导致依赖关系复杂
- **解决方案**: 拆分为三个独立的规划节点
  - `FRONTEND_PLANNER`: 专门处理前端任务（AMIS框架）
  - `BACKEND_PLANNER`: 专门处理后端任务（Groovy + Spring Boot）
  - `INTEGRATION_PLANNER`: 处理集成、测试和部署任务

### 2. 替换withStructuredOutput
- **问题**: LLM不支持withStructuredOutput方法
- **解决方案**: 使用Prompt Engineering技术
  - 在prompt中明确定义JSON格式要求
  - 使用代码块标记确保格式正确
  - 添加格式验证和错误处理

### 3. Prompt Engineering优化
- **改进内容**:
  - 结构化的prompt模板
  - 明确的角色定义和任务说明
  - 详细的输出格式要求
  - 示例和最佳实践指导
  - 错误处理和fallback机制

### 4. 模块化架构
- **文件结构**:
```
graph/
├── constants/           # 常量定义
├── types/              # 类型定义
├── utils/              # 工具函数
├── tools/              # 工具实现
├── nodes/              # 节点实现
├── openapi.ts          # API集成
├── graph-v2.ts         # 优化后的图定义
└── README.md           # 文档说明
```

## 文件结构说明

### 核心文件

#### `constants/index.ts`
- 定义所有枚举常量
- 节点名称、工具名称、任务类型等

#### `types/state.types.ts`
- 状态类型定义
- 任务接口定义
- 图片信息接口

#### `utils/`
- `llm.utils.ts`: LLM实例创建和配置
- `message.utils.ts`: 消息处理和图片集成

#### `tools/`
- `index.ts`: 工具创建和管理
- 文档获取工具
- 任务转交工具

#### `nodes/`
- `coordinator.node.ts`: 协调器节点
- `prd-parser.node.ts`: PRD解析节点
- `frontend-planner.node.ts`: 前端规划节点
- `backend-planner.node.ts`: 后端规划节点
- `integration-planner.node.ts`: 集成规划节点
- `tools.node.ts`: 工具执行节点
- `feedback.nodes.ts`: 反馈处理节点

### 工作流程

```mermaid
graph TD
    A[用户输入] --> B[COORDINATOR]
    B --> C{包含文档链接?}
    C -->|是| D[TOOLS - 获取文档]
    C -->|否| E{PRD请求?}
    D --> E
    E -->|是| F[PRD_PARSER]
    E -->|否| G[礼貌回复]
    F --> H{PRD完整?}
    H -->|否| I[PRD_FEEDBACK]
    H -->|是| J[FRONTEND_PLANNER]
    I --> F
    J --> K[BACKEND_PLANNER]
    K --> L[INTEGRATION_PLANNER]
    L --> M[PLANNER_FEEDBACK]
    M --> N{用户确认?}
    N -->|否| O{修改类型}
    N -->|是| P[完成]
    O --> J
    O --> K
    O --> L
```

## 任务ID分配规则

- **前端任务**: 1-99
- **后端任务**: 100-199

这样的分配避免了ID冲突，便于任务分类和依赖管理。

## 使用方法

### 1. 基本使用

```typescript
import { createOptimizedGraph } from './graph-v2';

// 创建图实例
const graph = createOptimizedGraph();

// 执行工作流
const result = await graph.invoke({
  originalPRD: "用户的PRD输入",
  messages: [],
  // ... 其他初始状态
});
```

### 2. 自定义配置

```typescript
import { createLLM, createStructuredLLM } from './utils/llm.utils';

// 创建自定义LLM配置
const customLLM = createLLM({
  temperature: 0.2,
  maxRetries: 5,
});
```

### 3. 节点扩展

```typescript
// 添加新的规划节点
export async function customPlannerNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  // 自定义逻辑
  return new Command({
    goto: 'nextNode',
    update: { /* 状态更新 */ },
  });
}
```

## 配置说明

### LLM配置
- **模型**: gpt-4o
- **温度**: 结构化输出使用0.0，创意任务使用0.7
- **重试**: 最多3次
- **超时**: 60秒

### 任务配置
- **前端技术栈**: AMIS + TypeScript
- **后端技术栈**: Groovy + Spring Boot + MySQL
- **部署方案**: Docker + K8s

## 错误处理

系统包含完善的错误处理机制：

1. **LLM调用失败**: 自动重试和fallback
2. **JSON解析失败**: 格式验证和修复
3. **网络错误**: 超时处理和错误提示
4. **工具执行失败**: 错误捕获和状态恢复

## 性能优化

1. **并行处理**: 前后端任务可以并行开发
2. **缓存机制**: LLM响应缓存（可选）
3. **增量更新**: 只更新变化的状态字段
4. **资源管理**: 合理的超时和重试策略

## 扩展性

系统设计支持以下扩展：

1. **新增节点**: 实现节点接口即可
2. **新增工具**: 在tools目录添加新工具
3. **自定义状态**: 扩展StateAnnotation
4. **多技术栈**: 添加新的规划器节点

## 测试建议

1. **单元测试**: 测试各个节点函数
2. **集成测试**: 测试完整工作流
3. **性能测试**: 测试大型PRD处理能力
4. **错误测试**: 测试各种异常情况

## 迁移指南

从旧版本迁移到新版本：

1. 更新导入路径
2. 使用新的图创建函数
3. 更新状态结构（如需要）
4. 测试工作流程

## 常见问题

### Q: 如何处理大型PRD文档？
A: 系统支持文档分块处理和图片内容分析。

### Q: 如何自定义任务模板？
A: 修改对应规划节点的prompt模板。

### Q: 如何添加新的技术栈支持？
A: 创建新的规划节点并更新工作流图。

### Q: 如何处理并发请求？
A: 每个请求使用独立的图实例和状态。

## 贡献指南

1. 遵循TypeScript编码规范
2. 添加适当的类型定义
3. 编写单元测试
4. 更新文档说明
5. 提交前运行linting检查

## 版本历史

- **v2.0**: 模块化重构，前后端分离，Prompt Engineering优化
- **v1.0**: 原始版本，单一graph.ts文件
